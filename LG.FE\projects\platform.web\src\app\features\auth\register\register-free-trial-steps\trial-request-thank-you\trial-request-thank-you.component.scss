@use "mixins";

// Galactic Journey Color Palette
$primary: #6366f1;
$primary-50: #eef2ff;
$primary-100: #e0e7ff;
$primary-600: #4f46e5;
$primary-700: #4338ca;

$success: #10b981;
$success-50: #ecfdf5;
$success-100: #d1fae5;
$success-600: #059669;
$success-700: #047857;

$warning: #f59e0b;
$warning-50: #fffbeb;
$warning-100: #fef3c7;

// Cosmic Color Palette
$cosmic-deep: #0f0f23;
$cosmic-blue: #1e1b4b;
$cosmic-purple: #4c1d95;
$cosmic-indigo: #312e81;
$cosmic-violet: #5b21b6;
$starlight: #333;
$nebula-pink: #ec4899;
$nebula-cyan: #06b6d4;
$cosmic-gold: #fbbf24;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Professional shadows
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Smooth transitions
$transition-fast: all 0.15s ease-out;
$transition-base: all 0.2s ease-out;
$transition-slow: all 0.3s ease-out;

:host {
  display: block;
  width: 100%;
}


// Compact Grouping Section
.grouping-section-compact {
  margin-top: 1.5rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: $shadow-sm;
  border: 1px solid $gray-200;
  animation: slide-up 0.6s ease-out 1s both;

  @include mixins.breakpoint(mobile) {
    margin-top: 1rem;
    padding: 0.75rem;
  }

  .grouping-header-compact {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    margin-bottom: 1rem;
    text-align: left;

    @include mixins.breakpoint(mobile) {
      gap: 0.375rem;
      margin-bottom: 0.75rem;
    }

    i {
      font-size: 1rem;
      flex-shrink: 0;

      @include mixins.breakpoint(mobile) {
        font-size: 0.875rem;
      }
    }

    .grouping-title-compact {
      font-size: 0.875rem;
      font-weight: 600;
      color: $gray-700;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
      }
    }
  }
}

// Galactic Animations
@keyframes cosmic-shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes cosmic-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes orbital-rotation {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }
  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

@keyframes planet-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-3px);
  }
}

@keyframes stellar-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
}

@keyframes success-bounce {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes check-draw {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

// Removed particle-float animation as particles were removed for compact design

@keyframes star-twinkle {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.2);
    opacity: 0.8;
  }
}

@keyframes slide-up {
  0% {
    opacity: 0;
    transform: translateY(30px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

