@defer (on immediate) {

<!-- Galactic Success Card Component -->
<app-galactic-success-card
  [config]="galacticConfig()"
  [size]="'medium'"
  [showOrbitalRing]="true">

  <div slot="primary-actions" class="flex gap-3 justify-content-center flex-wrap">
    <p-button
      [routerLink]="['/dashboard/parent/overview']"
      [rounded]="true"
      icon="pi pi-home"
      iconPos="left"
      label="Go to Dashboard"
      styleClass="p-button-outlined p-button-sm">
    </p-button>
    <p-button
      [routerLink]="['/dashboard/parent/students']"
      [rounded]="true"
      icon="pi pi-users"
      iconPos="left"
      label="View Students"
      styleClass="p-button-outlined p-button-sm">
    </p-button>
  </div>
</app-galactic-success-card>

}



<!-- Compact Grouping Section -->
<app-available-students-for-groups-list [studentId]="studentId()!" [teachingLanguageId]="teachingLanguageId()!" [teachingLanguageName]="teachingLanguageName()!"
  (studentSelected)="onStudentSelected($event)">
</app-available-students-for-groups-list>


