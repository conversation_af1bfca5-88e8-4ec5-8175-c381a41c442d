<div class="grid">

    <div class="col-12 xl:col-8 xxl:col-9 ">


        <div class="grid">

            <div class="col-12 xxl:col-12">

                <app-intro-gradient></app-intro-gradient>

            </div>

            <div class="col-12 xxl:col-12">

                <!-- <div class="">
                    <p-carousel [showIndicators]="false" [showNavigators]="mockBanners.length > 1 ? true : false"
                        [autoplayInterval]="5000" [circular]="mockBanners.length > 1 ? true : false"
                        [value]="mockBanners" [numVisible]="1" [numScroll]="1" [responsiveOptions]="responsiveOptions">
                        <ng-template let-product #item>
                            <div class="w-full ">
                                <div class="">
                                    <app-actionable-alert [iconUrl]="'/assets/images/graphic/' + product.imageSrc"
                                        [title]="product.title" [message]="product.message" buttonIcon="pi pi-cart-plus"
                                        [alertClass]="product.backgroundStyle + ' border-1 border-4'" imageClass="''"
                                        alertTextHeaderClass="" alertTextSubHeaderClass=""
                                        [buttonLabel]="product.buttonText" />
                                </div>
                            </div>
                        </ng-template>
                    </p-carousel>
                </div> -->
            </div>

            <div class="col-12 xxl:col-6">


                <app-overview-card-block cardClass="" mainTitle="Weekly Lesson Schedule" [showButton]="true"
                    buttonLabel="View All">
                    <div body>
                        <div class="w-full h-30rem overflow-y-auto">
                            @for (lesson of generalService.lessons(); track lesson.id) {
                            <div class="mb-2 mt-2 px-1">
                                <app-lesson-list-view-card [mini]="false" [lesson]="lesson" />
                                <!-- <app-lesson-mini-info-card [lesson]="lesson"></app-lesson-mini-info-card> -->
                            </div>
                            } @empty {
                            <li>There are no items.</li>
                            }
                        </div>
                    </div>
                </app-overview-card-block>

                <!-- <app-overview-card-block mainTitle="Welcome">
                    <div body>
                        <div class="w-full h-26rem overflow-y-auto">


                            <h3>Quick Actions</h3>
                            <div class="flex flex-column gap-2 justify-content-around">

                                <div (click)="generalService.goToBookLesson()"
                                    class="p-3 border-round shadow-2 flex align-items-center surface-card border-1 border-indigo-200 cursor-pointer hover:bg-indigo-50">
                                    <div class="bg-indigo-100 inline-flex align-items-center justify-content-center mr-3"
                                        style="width: 24px; height: 24px; border-radius: 10px;"><i
                                            class="pi pi-calendar text-indigo-600 text-sm"></i></div>
                                    <div><span class="text-indigo-500 text-lg font-medium mb-2">Schedule Lesson </span>
                                    </div>
                                    <div class="ml-auto">
                                        <i class="pi pi-chevron-right text-400"></i>
                                    </div>
                                </div>
    
                                <div (click)="this.registerService.goToRegisterNewStudent()"
                                    class="p-3 border-round shadow-2 flex align-items-center surface-card border-1 border-green-200 cursor-pointer hover:bg-green-50">
                                    <div class="bg-green-100 inline-flex align-items-center justify-content-center mr-3"
                                        style="width: 24px; height: 24px; border-radius: 10px;"><i
                                            class="pi pi-user text-green-600 text-sm"></i></div>
                                    <div><span class="text-green-500 text-lg font-medium mb-2">Register Student </span>
                                    </div>
                                    <div class="ml-auto">
                                        <i class="pi pi-chevron-right text-400"></i>
                                    </div>
                                </div>
    
                                <div (click)="openNewGroupDialog()"
                                    class="p-3 border-round shadow-2 flex align-items-center surface-card border-1 border-yellow-200 cursor-pointer hover:bg-yellow-100">
                                    <div class="bg-yellow-100 inline-flex align-items-center justify-content-center mr-3"
                                        style="width: 24px; height: 24px; border-radius: 10px;"><i
                                            class="pi pi-users text-yellow-600 text-sm"></i></div>
                                    <div><span class="text-yellow-500 text-lg font-medium mb-2">Create a Group</span>
                                    </div>
                                    <div class="ml-auto">
                                        <i class="pi pi-chevron-right text-400"></i>
                                    </div>
                                </div>
    
                            </div>
                        </div>
                    </div>
                </app-overview-card-block> -->
            </div>
            <div class="col-12 xxl:col-6">

                <app-overview-card-block mainTitle="Notifications" [showButton]="true" buttonLabel="View All"
                    (buttonClick)="handleButtonClick()">
                    <div body>
                        <div class="w-full h-30rem overflow-y-auto">
                            <ul class="list-none p-0 m-0">
                                @for (tab of generalService.dummyNotifications; track tab.title; let i = $index;) {
                                <app-notification-item-card [isRead]="false"
                                    [notification]="tab"></app-notification-item-card>
                                }
                            </ul>
                        </div>
                    </div>
                </app-overview-card-block>

            </div>
        </div>


        <div class="grid">

            <div class="col-12 xxl:col-6">


                <app-overview-card-block cardClass="mt-2" mainTitle="Latest Library Shares">
                    <div body>
                        <div class="w-full h-30rem overflow-y-auto">

                            <app-single-library [miniView]="true"></app-single-library>
                        </div>
                    </div>
                </app-overview-card-block>
            </div>

            <div class="col-12 xxl:col-6">


            </div>

        </div>
        <!-- ends grid -->







    </div>


    @defer (on immediate) {

    <div class="col-12 xl:col-4 xxl:col-3">

        <app-overview-card-block mainTitle="Calendar">
            <div body>
                <div class="w-full overflow-y-auto">

                    <app-calendar-mini-with-lessons></app-calendar-mini-with-lessons>
                </div>
            </div>
        </app-overview-card-block>

    </div>
    }@placeholder {
    <span>Loading...</span>
    }

</div>


@defer {

<div class="grid mt-2">

    <div class="col-12 md:col-6">

    </div>
    <div class="col-12 md:col-6">

        <!-- <div class="grid mt-2">
            <div class="col-12">
                <app-dashboard-lesson-status-icon-text></app-dashboard-lesson-status-icon-text>
            </div>
        </div> -->
    </div>
</div>
} @placeholder {
<span></span>
}

<!-- <div class="mt-4 px-3 lg:px-0">
    <h3 class="primary-purple-color m-0 px-2">Critical Notifications</h3>
    <p-carousel [value]="mockBanners" [numVisible]="1" [numScroll]="1" [circular]="true"
        [responsiveOptions]="responsiveOptions" [showIndicators]="false" [showNavigators]="true">
        <ng-template let-banner pTemplate="item">
            <div class="w-full px-2">
                <app-dashboard-banner-info-card [title]="banner.title" [message]="banner.message"
                    [buttonText]="banner.buttonText" [buttonClass]="banner.buttonClass"
                    [backgroundStyle]="banner.backgroundStyle" [imageSrc]="banner.imageSrc">
                </app-dashboard-banner-info-card>
            </div>
        </ng-template>
    </p-carousel>
</div> -->