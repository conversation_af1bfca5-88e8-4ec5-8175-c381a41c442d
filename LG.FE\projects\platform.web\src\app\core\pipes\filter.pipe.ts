import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'filter',
  standalone: true
})
export class FilterPipe implements PipeTransform {

  transform<T extends Record<string, any>>(data: T[], filterProperty: keyof T, filter: string): T[] {
    console.log(filterProperty);
    if (!filter) {
      return data;
    }
    console.log(filter);
    const filterValue = filter.toLowerCase();
    return filterValue
      ? data.filter(item => {
          const value = item[filterProperty];
          return typeof value === 'string' && value.toLowerCase().includes(filterValue);
        })
      : data;
  }

}